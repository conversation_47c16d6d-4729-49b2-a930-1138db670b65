<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LUC认证中心 - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .login-header p {
            color: #666;
            font-size: 14px;
        }
        
        .login-form {
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #ddd;
        }
        
        .divider span {
            background: white;
            padding: 0 15px;
            color: #666;
            font-size: 14px;
        }
        
        .oauth-buttons {
            display: flex;
            gap: 10px;
        }
        
        .oauth-btn {
            flex: 1;
            padding: 12px 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            color: #333;
            transition: all 0.3s;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .oauth-btn:hover {
            background-color: #f5f5f5;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .oauth-btn.gitee {
            color: #c71d23;
            border-color: #c71d23;
        }

        .oauth-btn.gitee:hover {
            background-color: #c71d23;
            color: white;
        }

        .oauth-btn.wechat {
            color: #07c160;
            border-color: #07c160;
        }

        .oauth-btn.wechat:hover {
            background-color: #07c160;
            color: white;
        }

        .oauth-icon {
            width: 16px;
            height: 16px;
            display: inline-block;
        }
        
        .register-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .register-link a {
            color: #667eea;
            text-decoration: none;
        }
        
        .register-link a:hover {
            text-decoration: underline;
        }
        
        .error-message {
            background-color: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>LUC认证中心</h1>
            <p>请登录您的账号</p>
        </div>
        
        <!-- 错误信息显示 -->
        <div th:if="${error}" class="error-message" th:text="${error}"></div>
        
        <!-- 用户名密码登录表单 -->
        <form class="login-form" th:action="@{/login}" method="post">
            <!-- CSRF 令牌 -->
            <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}"/>

            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required autocomplete="username">
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>
            <button type="submit" class="login-btn">登录</button>
        </form>

        <!-- 登录方式切换 -->
        <div class="login-switch" style="margin-top: 20px; text-align: center;">
            <button type="button" id="switchToSms" class="switch-btn" style="background: none; border: none; color: #007bff; cursor: pointer; text-decoration: underline;">使用手机验证码登录</button>
        </div>

        <!-- 短信登录区域（默认隐藏） -->
        <div id="smsLoginArea" style="display: none; margin-top: 20px;">
            <div class="divider">
                <span>手机验证码登录</span>
            </div>

            <div class="form-group" style="margin-top: 20px;">
                <label for="smsPhone">手机号</label>
                <input type="tel" id="smsPhone" placeholder="请输入手机号">
            </div>

            <div class="form-group" style="display: flex; gap: 10px;">
                <div style="flex: 1;">
                    <label for="smsCodeInput">验证码</label>
                    <input type="text" id="smsCodeInput" placeholder="请输入验证码">
                </div>
                <button type="button" id="getSmsCodeBtn" class="get-code-btn" style="margin-top: 25px; padding: 10px 15px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">获取验证码</button>
            </div>

            <button type="button" id="smsLoginBtn" class="login-btn" style="margin-top: 15px;">短信登录</button>

            <div style="text-align: center; margin-top: 10px;">
                <button type="button" id="switchToPassword" class="switch-btn" style="background: none; border: none; color: #007bff; cursor: pointer; text-decoration: underline;">返回密码登录</button>
            </div>
        </div>

        <!-- 分隔线 -->
        <div class="divider" style="margin-top: 30px;">
            <span>或使用第三方账号登录</span>
        </div>
        
        <!-- 第三方登录按钮 -->
        <div class="oauth-buttons">
            <a href="/oauth2/authorization/gitee" class="oauth-btn gitee">
                <svg class="oauth-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 0.297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"/>
                </svg>
                Gitee登录
            </a>
            <a href="/oauth2/authorization/wechat" class="oauth-btn wechat">
                <svg class="oauth-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.900 7.852.24-.71-3.842-4.493-6.616-8.596-6.616z"/>
                </svg>
                微信登录
            </a>
        </div>
        
        <!-- 注册链接 -->
        <div class="register-link">
            <p>还没有账号？ <a href="/register">立即注册</a></p>
        </div>
    </div>
    
    <script>
        // 检查URL参数中是否有错误信息
        const urlParams = new URLSearchParams(window.location.search);
        const error = urlParams.get('error');
        if (error && !document.querySelector('.error-message')) {
            // 如果URL中有error参数但页面上没有显示错误消息，则动态创建
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = '登录失败，请检查用户名和密码';

            const loginForm = document.querySelector('.login-form');
            loginForm.parentNode.insertBefore(errorDiv, loginForm);
        }

        // 为第三方登录按钮添加点击事件（可选：添加加载状态）
        document.querySelectorAll('.oauth-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.opacity = '0.7';
                this.style.pointerEvents = 'none';

                // 3秒后恢复按钮状态（防止用户重复点击）
                setTimeout(() => {
                    this.style.opacity = '1';
                    this.style.pointerEvents = 'auto';
                }, 3000);
            });
        });

        // 登录方式切换
        const switchToSms = document.getElementById('switchToSms');
        const switchToPassword = document.getElementById('switchToPassword');
        const smsLoginArea = document.getElementById('smsLoginArea');
        const passwordForm = document.querySelector('.login-form');

        switchToSms.addEventListener('click', function() {
            smsLoginArea.style.display = 'block';
            passwordForm.style.display = 'none';
        });

        switchToPassword.addEventListener('click', function() {
            smsLoginArea.style.display = 'none';
            passwordForm.style.display = 'block';
        });

        // 获取短信验证码功能
        let countdown = 0;
        const getSmsCodeBtn = document.getElementById('getSmsCodeBtn');
        const smsPhoneInput = document.getElementById('smsPhone');

        getSmsCodeBtn.addEventListener('click', function() {
            const phone = smsPhoneInput.value.trim();

            if (!phone) {
                alert('请输入手机号');
                return;
            }

            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('手机号格式不正确');
                return;
            }

            if (countdown > 0) {
                return;
            }

            // 发送验证码请求
            fetch('/sms/code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `phone=${encodeURIComponent(phone)}&type=login`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('验证码发送成功！测试验证码：' + data.code);
                    startCountdown();
                } else {
                    alert(data.message || '验证码发送失败');
                }
            })
            .catch(error => {
                console.error('发送验证码失败:', error);
                alert('验证码发送失败，请稍后重试');
            });
        });

        // 短信登录
        const smsLoginBtn = document.getElementById('smsLoginBtn');
        const smsCodeInput = document.getElementById('smsCodeInput');

        smsLoginBtn.addEventListener('click', function() {
            const phone = smsPhoneInput.value.trim();
            const smsCode = smsCodeInput.value.trim();

            if (!phone || !smsCode) {
                alert('请输入手机号和验证码');
                return;
            }

            // 将手机号和验证码填入原表单并提交
            document.getElementById('username').value = phone;
            document.getElementById('password').value = smsCode;
            document.querySelector('.login-form').submit();
        });

        function startCountdown() {
            countdown = 60;
            getSmsCodeBtn.disabled = true;

            const timer = setInterval(() => {
                getSmsCodeBtn.textContent = `${countdown}秒后重试`;
                countdown--;

                if (countdown < 0) {
                    clearInterval(timer);
                    getSmsCodeBtn.disabled = false;
                    getSmsCodeBtn.textContent = '获取验证码';
                }
            }, 1000);
        }
    </script>
</body>
</html>
