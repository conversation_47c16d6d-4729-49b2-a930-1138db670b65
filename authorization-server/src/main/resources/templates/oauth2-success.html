<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录成功 - LUC认证中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .success-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: #4CAF50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            animation: bounce 0.6s ease-out;
        }
        
        .success-icon svg {
            width: 40px;
            height: 40px;
            fill: white;
        }
        
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0, 0, 0);
            }
            40%, 43% {
                transform: translate3d(0, -20px, 0);
            }
            70% {
                transform: translate3d(0, -10px, 0);
            }
            90% {
                transform: translate3d(0, -4px, 0);
            }
        }
        
        .success-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .success-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        .user-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: block;
            border: 3px solid #e9ecef;
        }
        
        .user-detail {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .user-detail:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .user-label {
            font-weight: 600;
            color: #495057;
        }
        
        .user-value {
            color: #6c757d;
            word-break: break-all;
        }
        
        .provider-badge {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .provider-badge.gitee {
            background: #c71d23;
        }
        
        .provider-badge.wechat {
            background: #07c160;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-1px);
        }
        
        .countdown {
            margin-top: 20px;
            font-size: 14px;
            color: #6c757d;
        }
        
        @media (max-width: 480px) {
            .success-container {
                padding: 30px 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <!-- 成功图标 -->
        <div class="success-icon">
            <svg viewBox="0 0 24 24">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
        </div>
        
        <!-- 成功标题 -->
        <h1 class="success-title">登录成功！</h1>
        <p class="success-message">
            恭喜您，已成功通过第三方账号登录 LUC 认证中心
        </p>
        
        <!-- 用户信息 -->
        <div class="user-info">
            <img th:if="${userAvatar}" th:src="${userAvatar}" th:alt="${userName}" class="user-avatar">
            
            <div class="user-detail">
                <span class="user-label">用户名称：</span>
                <span class="user-value" th:text="${userName ?: '未提供'}">未提供</span>
            </div>
            
            <div class="user-detail" th:if="${userLogin}">
                <span class="user-label">登录账号：</span>
                <span class="user-value" th:text="${userLogin}">未提供</span>
            </div>
            
            <div class="user-detail" th:if="${userEmail}">
                <span class="user-label">邮箱地址：</span>
                <span class="user-value" th:text="${userEmail}">未提供</span>
            </div>
            
            <div class="user-detail">
                <span class="user-label">登录方式：</span>
                <span class="provider-badge" th:class="'provider-badge ' + ${provider}" th:text="${provider}">第三方登录</span>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
            <a href="/" class="btn btn-primary">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
                返回首页
            </a>
            <a href="/logout" class="btn btn-secondary">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                </svg>
                退出登录
            </a>
        </div>
        
        <!-- 自动跳转倒计时 -->
        <div class="countdown">
            <span id="countdown-text">5秒后自动跳转到首页</span>
        </div>
    </div>
    
    <script>
        // 自动跳转倒计时
        let countdown = 5;
        const countdownElement = document.getElementById('countdown-text');
        
        const timer = setInterval(() => {
            countdown--;
            if (countdown > 0) {
                countdownElement.textContent = `${countdown}秒后自动跳转到首页`;
            } else {
                clearInterval(timer);
                window.location.href = '/';
            }
        }, 1000);
        
        // 点击任意按钮时取消自动跳转
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', () => {
                clearInterval(timer);
                countdownElement.textContent = '已取消自动跳转';
            });
        });
    </script>
</body>
</html>
